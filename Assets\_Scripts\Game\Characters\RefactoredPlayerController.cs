using System;
using UnityEngine;

namespace AnchorLight.Game.Characters
{
    /// <summary>
    /// Refactored PlayerController following clean architecture principles.
    /// This demonstrates how the monolithic controller should be broken down.
    ///
    /// Key improvements:
    /// - Uses dependency injection through ServiceLocator
    /// - Separates concerns into focused components
    /// - Follows single responsibility principle
    /// - Easier to test and maintain
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D), typeof(Collider2D))]
    public class RefactoredPlayerController
        : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            IPlayerController,
            IPlayerContextProvider
    {
        [Header("Configuration")]
        [SerializeField]
        private MovementStats _movementStats;

        [SerializeField]
        private JumpStats _jumpStats;

        [SerializeField]
        private DashStats _dashStats;

        [SerializeField]
        private SwimmingStats _swimmingStats;

        [SerializeField]
        private WallClimbingStats _wallClimbingStats;

        [SerializeField]
        private GlidingStats _glidingStats;

        [Header("References")]
        [SerializeField]
        private Transform _handAnchor;

        [SerializeField]
        private PlayerAnimator _playerAnimator;

        // Services (injected via ServiceLocator)
        private IInputService _inputService;
        private IPhysicsService _physicsService;

        // Components
        private Rigidbody2D _rb;
        private CapsuleCollider2D _col;
        private PlayerContext _context;

        // Movement components
        private GroundMovementComponent _groundMovement;
        private AirMovementComponent _airMovement;
        private SwimmingMovementComponent _swimmingMovement;
        private WallClimbingMovementComponent _wallClimbingMovement;

        // Abilities
        private JumpAbility _jumpAbility;
        private DashAbility _dashAbility;

        // State
        private Vector2 _frameVelocity;
        private float _time;

        // Events
        public event Action<bool, float> GroundedChanged;
        public event Action Jumped;

        // Interface implementation
        public Vector2 FrameInput => _inputService?.CurrentInput.Move ?? Vector2.zero;
        public bool IsSwimming => _context?.IsSwimming ?? false;
        public bool IsDiving => false; // TODO: Implement diving as separate ability
        public bool IsWallClimbing => _context?.IsWallClimbing ?? false;
        public bool IsRopeClimbing => _context?.IsRopeClimbing ?? false;

        private void Awake()
        {
            InitializeComponents();
            InitializeContext();
            InitializeMovementComponents();
            InitializeAbilities();
        }

        private void Start()
        {
            // Get services from ServiceLocator
            _inputService = ServiceLocator.Get<IInputService>();
            _physicsService = ServiceLocator.Get<IPhysicsService>();

            if (_inputService == null)
                Debug.LogError(
                    "InputService not found. Make sure GameBootstrap is in the scene.",
                    this
                );
            if (_physicsService == null)
                Debug.LogError(
                    "PhysicsService not found. Make sure GameBootstrap is in the scene.",
                    this
                );
        }

        private void Update()
        {
            _time += Time.deltaTime;

            // Update input
            _inputService?.UpdateInput();

            // Update abilities
            _jumpAbility?.Update(Time.deltaTime);
            _dashAbility?.Update(Time.deltaTime);
        }

        private void FixedUpdate()
        {
            if (_inputService == null || _physicsService == null)
                return;

            UpdateCollisions();
            UpdateAbilities();
            UpdateMovement();
            ApplyMovement();
        }

        private void InitializeComponents()
        {
            _rb = GetComponent<Rigidbody2D>();
            _col = GetComponent<CapsuleCollider2D>();
        }

        private void InitializeContext()
        {
            _context = new PlayerContext(this, _rb, _col);
        }

        private void InitializeMovementComponents()
        {
            _groundMovement = new GroundMovementComponent(_movementStats);
            _airMovement = new AirMovementComponent(_movementStats, _jumpStats, _glidingStats);
            _swimmingMovement = new SwimmingMovementComponent(_swimmingStats);
            _wallClimbingMovement = new WallClimbingMovementComponent(_wallClimbingStats);
        }

        private void InitializeAbilities()
        {
            _jumpAbility = new JumpAbility(_jumpStats, _movementStats);
            _dashAbility = new DashAbility(_dashStats);

            // Subscribe to events
            _jumpAbility.Jumped += () => Jumped?.Invoke();
        }

        private void UpdateCollisions()
        {
            // Use physics service for collision detection
            var wasGrounded = _context.IsGrounded;
            var isGrounded = _physicsService.IsGrounded(
                _col,
                ~LayerMask.GetMask("Player"),
                _movementStats.GrounderDistance
            );

            if (wasGrounded != isGrounded)
            {
                _context.SetGrounded(isGrounded);
                GroundedChanged?.Invoke(isGrounded, Mathf.Abs(_frameVelocity.y));

                if (isGrounded)
                    _jumpAbility.OnGrounded();
                else
                    _jumpAbility.OnLeftGround(_time);
            }
        }

        private void UpdateAbilities()
        {
            var input = _inputService.CurrentInput;

            // Try to activate abilities
            _jumpAbility.TryActivate(_context, input);
            _dashAbility.TryActivate(_context, input);

            // Update abilities
            _jumpAbility.FixedUpdate(Time.fixedDeltaTime);
            _dashAbility.FixedUpdate(Time.fixedDeltaTime);

            // Sync ability states with context
            _context.SetDashing(_dashAbility.IsActive);
        }

        private void UpdateMovement()
        {
            var input = _inputService.CurrentInput;

            // Determine which movement component should be active
            if (_dashAbility.IsActive)
            {
                _frameVelocity = _dashAbility.GetDashVelocity();
            }
            else if (_swimmingMovement.CanBeActive(_context))
            {
                _frameVelocity = _swimmingMovement.CalculateVelocity(
                    _frameVelocity,
                    input,
                    Time.fixedDeltaTime
                );
            }
            else if (_wallClimbingMovement.CanBeActive(_context))
            {
                _frameVelocity = _wallClimbingMovement.CalculateVelocity(
                    _frameVelocity,
                    input,
                    Time.fixedDeltaTime
                );
            }
            else if (_groundMovement.CanBeActive(_context))
            {
                _frameVelocity = _groundMovement.CalculateVelocity(
                    _frameVelocity,
                    input,
                    Time.fixedDeltaTime
                );
            }
            else if (_airMovement.CanBeActive(_context))
            {
                _frameVelocity = _airMovement.CalculateVelocity(
                    _frameVelocity,
                    input,
                    Time.fixedDeltaTime
                );
            }
        }

        private void ApplyMovement()
        {
            _rb.linearVelocity = _frameVelocity;
        }

        // IPlayerContextProvider implementation
        public Rigidbody2D RB => _rb;
        public Collider2D Col => _col;
        public float LastFacing => _inputService?.LastFacing ?? 1f;
        public bool IsDashing => _dashAbility?.IsActive ?? false;

        // Temporary properties for backward compatibility during transition
        public bool _isDashing => _dashAbility?.IsActive ?? false;
        public float _lastFacing => _inputService?.LastFacing ?? 1f;
    }
}
